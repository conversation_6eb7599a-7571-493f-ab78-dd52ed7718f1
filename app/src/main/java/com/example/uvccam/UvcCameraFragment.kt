package com.example.uvccam

import android.content.Context
import android.content.res.Configuration
import android.hardware.usb.UsbDevice
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.jiangdg.ausbc.MultiCameraClient
import com.jiangdg.ausbc.base.CameraFragment
import com.jiangdg.ausbc.callback.ICameraStateCallBack
import com.jiangdg.ausbc.camera.CameraUVC
import com.jiangdg.ausbc.camera.bean.CameraRequest
import com.jiangdg.ausbc.render.env.RotateType
import com.jiangdg.ausbc.widget.AspectRatioTextureView
import com.jiangdg.ausbc.widget.IAspectRatio
import com.example.uvccam.databinding.FragmentUvcCameraBinding
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

class UvcCameraFragment : CameraFragment() {
    private var _binding: FragmentUvcCameraBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentUvcCameraBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // The parent CameraFragment will handle camera initialization
    }

    /**
     * Allow runtime adjustment of camera rotation if needed
     * Call this method to change rotation without restarting camera
     */
    fun adjustCameraRotation(rotateType: RotateType) {
        setRotateType(rotateType)
    }

    override fun getRootView(inflater: LayoutInflater, container: ViewGroup?): View {
        return onCreateView(inflater, container, null)
    }

    override fun getCameraView(): IAspectRatio? {
        return binding.tvCameraRender
    }

    override fun getCameraViewContainer(): ViewGroup? {
        return binding.container
    }

    override fun generateCamera(ctx: Context, device: UsbDevice): MultiCameraClient.ICamera {
        return CameraUVC(ctx, device)
    }

    override fun getCameraRequest(): CameraRequest {
        // Get optimal resolution based on device screen
        val (previewWidth, previewHeight) = getOptimalPreviewSize()
        Toast.makeText(requireContext(), "Dimension: $previewHeight, $previewWidth", Toast.LENGTH_SHORT).show()
        return CameraRequest.Builder()
            // Dynamic resolution based on actual camera capabilities
            .setPreviewWidth(previewWidth)
            .setPreviewHeight(previewHeight)
            .setRenderMode(CameraRequest.RenderMode.OPENGL)
            // Set 270-degree rotation (90 + 180) to correct orientation issue
            .setDefaultRotateType(RotateType.ANGLE_270)
            .setAudioSource(CameraRequest.AudioSource.SOURCE_SYS_MIC)
            .setPreviewFormat(CameraRequest.PreviewFormat.FORMAT_MJPEG)
            // Disable aspect ratio enforcement to allow view to fill entire screen
            // Center crop will be applied at OpenGL level to prevent distortion
            .setAspectRatioShow(false)
            .setCaptureRawImage(true)
            .setRawPreviewData(false)
            .create()
    }

    /**
     * Enable center crop mode to fill screen without distortion
     */
    private fun enableCenterCropMode() {
        lifecycleScope.launch {
            try {
                val cameraRequest = getCameraRequest()
                val cameraWidth = cameraRequest.previewWidth
                val cameraHeight = cameraRequest.previewHeight
                
                getCurrentCamera()?.enableCenterCrop(cameraWidth, cameraHeight)
                
                android.util.Log.i(TAG, "Center crop enabled: ${cameraWidth}x${cameraHeight}")
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Failed to enable center crop: ${e.message}")
            }
        }
    }

    /**
     * Get optimal preview size by querying actual camera capabilities
     * Falls back to screen-based selection if camera query fails
     */
    private fun getOptimalPreviewSize(): Pair<Int, Int> {
        // Phase 1: Try dynamic camera-based selection
        val dynamicSize = runBlocking { tryDynamicPreviewSizeSelection() }
        if (dynamicSize != null) {
            android.util.Log.i(TAG, "Using dynamic camera-based resolution: ${dynamicSize.first}x${dynamicSize.second}")
            return dynamicSize
        }
        
        // Phase 2: Fallback to enhanced screen-based selection
        android.util.Log.i(TAG, "Camera unavailable, using fallback screen-based selection")
        return getFallbackPreviewSize()
    }

    /**
     * Attempt to select optimal preview size based on actual camera capabilities
     * @return Pair<width, height> if successful, null if camera unavailable or query fails
     */
    private suspend fun tryDynamicPreviewSizeSelection(): Pair<Int, Int>? {
        return try {
            // Get current camera instance
            val camera = getCurrentCamera()
            if (camera == null) {
                android.util.Log.d(TAG, "Camera not available for dynamic resolution selection")
                return null
            }

            // Query all supported preview sizes
            val supportedSizes = camera.getAllPreviewSizes()
            if (supportedSizes.isNullOrEmpty()) {
                android.util.Log.d(TAG, "No supported preview sizes available from camera")
                return null
            }
            
            android.util.Log.d(TAG, "Camera supports ${supportedSizes.size} resolutions: ${supportedSizes.joinToString { "${it.width}x${it.height}" }}")

            // Get screen dimensions for comparison
            val screenDimensions = getScreenDimensions() ?: return null
            
            // Apply intelligent selection algorithm
            val optimalSize = selectOptimalFromSupportedSizes(supportedSizes, screenDimensions)
            android.util.Log.i(TAG, "Selected optimal size from ${supportedSizes.size} supported: ${optimalSize.first}x${optimalSize.second}")
            
            optimalSize
        } catch (e: Exception) {
            android.util.Log.e(TAG, "Error in dynamic preview size selection: ${e.message}")
            null
        }
    }

    /**
     * Intelligent selection algorithm to pick optimal resolution from supported sizes
     */
    private fun selectOptimalFromSupportedSizes(
        supportedSizes: MutableList<com.jiangdg.ausbc.camera.bean.PreviewSize>,
        screenDimensions: ScreenDimensions
    ): Pair<Int, Int> {
        val screenAspect = screenDimensions.aspectRatio
        
        // Score each supported size and find the best match
        val scoredSizes = supportedSizes.map { size ->
            val aspectScore = calculateAspectRatioScore(size, screenAspect)
            val qualityScore = calculateQualityScore(size, screenDimensions)
            val combinedScore = (aspectScore * 0.6f) + (qualityScore * 0.4f)
            
            ScoredPreviewSize(size, aspectScore, qualityScore, combinedScore)
        }
        
        // Sort by combined score (highest first) and pick the best
        val bestSize = scoredSizes.maxByOrNull { it.combinedScore }?.previewSize
            ?: supportedSizes.first() // Fallback to first available if scoring fails
            
        // Log detailed scoring information
        if (android.util.Log.isLoggable(TAG, android.util.Log.DEBUG)) {
            val bestScore = scoredSizes.maxByOrNull { it.combinedScore }
            android.util.Log.d(TAG, "Resolution scoring results:")
            scoredSizes.sortedByDescending { it.combinedScore }.take(3).forEach { scored ->
                android.util.Log.d(TAG, "  ${scored.previewSize.width}x${scored.previewSize.height}: aspect=${"%.2f".format(scored.aspectScore)}, quality=${"%.2f".format(scored.qualityScore)}, combined=${"%.2f".format(scored.combinedScore)}")
            }
            android.util.Log.i(TAG, "Selected: ${bestSize.width}x${bestSize.height} (aspect=${"%.2f".format(bestScore?.aspectScore ?: 0f)}, quality=${"%.2f".format(bestScore?.qualityScore ?: 0f)})")
        } else {
            android.util.Log.i(TAG, "Selected optimal resolution: ${bestSize.width}x${bestSize.height}")
        }
        return Pair(bestSize.width, bestSize.height)
    }

    /**
     * Calculate aspect ratio score (higher is better match)
     */
    private fun calculateAspectRatioScore(
        size: com.jiangdg.ausbc.camera.bean.PreviewSize, 
        screenAspect: Float
    ): Float {
        val cameraAspect = size.width.toFloat() / size.height.toFloat()
        val aspectDifference = kotlin.math.abs(screenAspect - cameraAspect)
        return kotlin.math.max(0f, (ASPECT_RATIO_TOLERANCE - aspectDifference) / ASPECT_RATIO_TOLERANCE)
    }

    /**
     * Calculate quality score based on resolution preference (higher is better)
     */
    private fun calculateQualityScore(
        size: com.jiangdg.ausbc.camera.bean.PreviewSize,
        screenDimensions: ScreenDimensions
    ): Float {
        val pixels = size.width * size.height
        val targetPixels = if (screenDimensions.isHighEnd) TARGET_1080P_PIXELS else TARGET_720P_PIXELS
        
        // Score based on how close to target resolution (penalty for going too far above or below)
        val pixelRatio = pixels.toFloat() / targetPixels.toFloat()
        return when {
            pixelRatio > MAX_RESOLUTION_RATIO -> 0.3f // Too high resolution, performance penalty
            pixelRatio >= 0.8f && pixelRatio <= 1.2f -> 1.0f // Perfect range
            pixelRatio >= 0.5f -> 0.8f // Acceptable range
            else -> 0.5f // Too low resolution
        }
    }

    /**
     * Enhanced fallback preview size selection based on screen characteristics
     */
    private fun getFallbackPreviewSize(): Pair<Int, Int> {
        val screenDimensions = getScreenDimensions()
        if (screenDimensions == null) {
            android.util.Log.w(TAG, "Unable to get screen dimensions, using safe default")
            return Pair(DEFAULT_FALLBACK_WIDTH, DEFAULT_FALLBACK_HEIGHT)
        }

        return when {
            // Ultra-high resolution devices with ultra-wide screens
            screenDimensions.isHighEnd && screenDimensions.aspectRatio > 2.1f -> {
                android.util.Log.d(TAG, "Ultra-wide high-end device detected")
                Pair(1920, 1080) // High quality for center crop
            }
            // High resolution devices  
            screenDimensions.isHighEnd -> {
                android.util.Log.d(TAG, "High-end device detected")
                Pair(1920, 1080) // Full HD
            }
            // Medium resolution devices
            screenDimensions.width >= 1800 -> {
                android.util.Log.d(TAG, "Medium resolution device detected")
                Pair(1280, 720) // HD
            }
            // Lower resolution devices
            else -> {
                android.util.Log.d(TAG, "Standard resolution device detected")
                Pair(1280, 720) // HD
            }
        }
    }

    /**
     * Get screen dimensions with error handling
     */
    private fun getScreenDimensions(): ScreenDimensions? {
        return try {
            // Check if fragment is still attached to avoid crashes
            if (!isAdded || activity == null) {
                android.util.Log.w(TAG, "Fragment not attached, cannot get screen dimensions")
                return null
            }
            
            val displayMetrics = DisplayMetrics()
            @Suppress("DEPRECATION")
            requireActivity().windowManager.defaultDisplay.getMetrics(displayMetrics)
            
            val width = displayMetrics.widthPixels
            val height = displayMetrics.heightPixels
            
            // Validate dimensions
            if (width <= 0 || height <= 0) {
                android.util.Log.e(TAG, "Invalid screen dimensions: ${width}x${height}")
                return null
            }
            
            val aspectRatio = width.toFloat() / height.toFloat()
            val isHighEnd = width >= 2400
            
            android.util.Log.d(TAG, "Screen dimensions: ${width}x${height}, aspect ratio: ${"%.2f".format(aspectRatio)}, high-end: $isHighEnd")
            ScreenDimensions(width, height, aspectRatio, isHighEnd)
        } catch (e: Exception) {
            android.util.Log.e(TAG, "Error getting screen dimensions: ${e.message}")
            null
        }
    }

    /**
     * Data classes for resolution selection
     */
    private data class ScreenDimensions(
        val width: Int,
        val height: Int, 
        val aspectRatio: Float,
        val isHighEnd: Boolean
    )

    private data class ScoredPreviewSize(
        val previewSize: com.jiangdg.ausbc.camera.bean.PreviewSize,
        val aspectScore: Float,
        val qualityScore: Float,
        val combinedScore: Float
    )

    override fun onCameraState(
        self: MultiCameraClient.ICamera,
        code: ICameraStateCallBack.State,
        msg: String?
    ) {
        // Check if fragment is attached to avoid crashes
        if (!isAdded || context == null) {
            return
        }

        try {
            when (code) {
                ICameraStateCallBack.State.OPENED -> {
                    Toast.makeText(requireContext(), "摄像头已开启", Toast.LENGTH_SHORT).show()
                    // Enable center crop when camera opens successfully
                    enableCenterCropMode()
                }
                ICameraStateCallBack.State.CLOSED -> {
                    Toast.makeText(requireContext(), "摄像头已关闭", Toast.LENGTH_SHORT).show()
                }
                ICameraStateCallBack.State.ERROR -> {
                    Toast.makeText(requireContext(), "错误: $msg", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: IllegalStateException) {
            // Fragment not attached to context - ignore the callback
            android.util.Log.w(TAG, "Fragment not attached when handling camera state: $code")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val TAG = "UvcCameraFragment"
        
        // Resolution selection constants
        private const val ASPECT_RATIO_TOLERANCE = 0.15f
        private const val TARGET_1080P_PIXELS = 1920 * 1080
        private const val TARGET_720P_PIXELS = 1280 * 720
        private const val MAX_RESOLUTION_RATIO = 1.5f // Don't go more than 1.5x target
        
        // Fallback defaults
        private const val DEFAULT_FALLBACK_WIDTH = 1280
        private const val DEFAULT_FALLBACK_HEIGHT = 720
    }
}