/*
 * Copyright 2017-2023 Jiangdg
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jiangdg.ausbc.base

import android.content.Context
import android.graphics.SurfaceTexture
import android.hardware.usb.UsbDevice
import android.os.Handler
import android.os.Looper
import android.view.*
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RelativeLayout
import com.jiangdg.ausbc.MultiCameraClient
import com.jiangdg.ausbc.camera.bean.PreviewSize
import com.jiangdg.ausbc.camera.bean.CameraRequest
import com.jiangdg.ausbc.callback.*
import com.jiangdg.ausbc.camera.CameraUVC
import com.jiangdg.ausbc.render.effect.AbstractEffect
import com.jiangdg.ausbc.render.env.RotateType
import com.jiangdg.ausbc.utils.Logger
import com.jiangdg.ausbc.widget.IAspectRatio
import com.jiangdg.usb.USBMonitor
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.ConcurrentHashMap
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*

/**Extends from BaseFragment for one uvc camera
 *
 * <AUTHOR> by jiangdg on 2023/2/3
 */
abstract class CameraFragment : BaseFragment(), ICameraStateCallBack {
    private var mCameraView: IAspectRatio? = null
    private var mCameraClient: MultiCameraClient? = null
    private val mCameraMap = hashMapOf<Int, MultiCameraClient.ICamera>()
    // StateFlow for reactive camera state management - replaces CompletableDeferred
    private val _currentCameraState = MutableStateFlow<MultiCameraClient.ICamera?>(null)
    val currentCameraState: StateFlow<MultiCameraClient.ICamera?> = _currentCameraState.asStateFlow()

    private val mRequestPermission: AtomicBoolean by lazy {
        AtomicBoolean(false)
    }
    private val mMainHandler by lazy { Handler(Looper.getMainLooper()) }
    
    // Fragment-level coroutine scope for async operations
    private val fragmentScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

    // Reactive state management for camera connection flow
    data class CameraConnectionState(
        val isSurfaceReady: Boolean = false,
        val isCameraConnected: Boolean = false,
        val connectedDevice: UsbDevice? = null
    )
    
    private val _connectionState = MutableStateFlow(CameraConnectionState())
    val connectionState: StateFlow<CameraConnectionState> = _connectionState.asStateFlow()
    
    // Convenience properties for backward compatibility
    private val isSurfaceReady: Boolean get() = _connectionState.value.isSurfaceReady
    private val isCameraConnected: Boolean get() = _connectionState.value.isCameraConnected
    private val connectedDevice: UsbDevice? get() = _connectionState.value.connectedDevice
    
    // Helper methods to update reactive state
    private fun updateSurfaceReady(ready: Boolean) {
        _connectionState.value = _connectionState.value.copy(isSurfaceReady = ready)
    }
    
    private fun updateCameraConnected(connected: Boolean, device: UsbDevice? = null) {
        _connectionState.value = _connectionState.value.copy(
            isCameraConnected = connected,
            connectedDevice = if (connected) device else null
        )
    }

    // Permission state cache to avoid redundant checks (thread-safe)
    private val mPermissionCache = ConcurrentHashMap<Int, Boolean>()
    private var mLastPermissionCheckTime = 0L

    override fun initData() {
        super.initData()
        // Initialize USB monitor early to start device detection immediately
        // This eliminates the delay between fragment creation and device detection
        registerMultiCamera()
    }

    override fun initView() {
        when (val cameraView = getCameraView()) {
            is TextureView -> {
                handleTextureView(cameraView)
                cameraView
            }
            is SurfaceView -> {
                handleSurfaceView(cameraView)
                cameraView
            }
            else -> {
                null
            }
        }.apply {
            mCameraView = this
            // offscreen render
            if (this == null) {
                registerMultiCamera()
                return
            }
        }?.also { view->
            getCameraViewContainer()?.apply {
                removeAllViews()
                addView(view, getViewLayoutParams(this))
            }
        }
    }

    override fun clear() {
        // Clear camera state callback synchronously since getCurrentCamera() is no longer suspend
        getCurrentCamera()?.setCameraStateCallBack(null)
        
        // Clear StateFlow camera state
        _currentCameraState.value = null
        
        // Cancel fragment coroutine scope to prevent memory leaks
        fragmentScope.cancel()
        
        // Reset state flags using reactive state
        _connectionState.value = CameraConnectionState()
        
        // Clear permission cache
        mPermissionCache.clear()
        unRegisterMultiCamera()
    }

    protected fun registerMultiCamera() {
        mCameraClient = MultiCameraClient(requireContext(), object : IDeviceConnectCallBack {
            override fun onAttachDev(device: UsbDevice?) {
                device ?: return
                context?.let {
                    if (mCameraMap.containsKey(device.deviceId)) {
                        return
                    }
                    generateCamera(it, device).apply {
                        mCameraMap[device.deviceId] = this
                    }

                    // Start surface preparation in parallel with permission request
                    // This reduces delay after permission is granted
                    prepareSurfaceForCamera()

                    // Initiate permission request when device insertion is detected
                    // If you want to open the specified camera, you need to override getDefaultCamera()
                    if (mRequestPermission.get()) {
                        Logger.i(TAG, "Permission request already in progress, skipping duplicate request")
                        return@let
                    }

                    // Check cached permission first to avoid redundant system calls
                    if (hasCachedPermission(device)) {
                        Logger.i(TAG, "Device has cached permission, skipping permission request")
                        return@let
                    }

                    getDefaultCamera()?.apply {
                        if (vendorId == device.vendorId && productId == device.productId) {
                            Logger.i(TAG, "default camera pid: $productId, vid: $vendorId")
                            requestPermission(device)
                        }
                        return@let
                    }
                    // Only request permission if not already requested by strategy layer
                    if (!mRequestPermission.get()) {
                        requestPermission(device)
                    }
                }
            }

            override fun onDetachDec(device: UsbDevice?) {
                mCameraMap.remove(device?.deviceId)?.apply {
                    // Clear callback to prevent further state events
                    setCameraStateCallBack(null)
                    setUsbControlBlock(null)
                }
                // Reset connection state using reactive state
                updateCameraConnected(false)
                // Reset permission state to allow new requests
                mRequestPermission.set(false)
                // Clear camera state when device is detached
                _currentCameraState.value = null
                Logger.i(TAG, "Device detached, permission state reset for device = ${device?.deviceName}")
            }

            override fun onConnectDev(device: UsbDevice?, ctrlBlock: USBMonitor.UsbControlBlock?) {
                device ?: return
                ctrlBlock ?: return
                context ?: return
                mCameraMap[device.deviceId]?.apply {
                    setUsbControlBlock(ctrlBlock)
                }?.also { camera ->
                    // Update camera state using StateFlow (thread-safe)
                    _currentCameraState.value = camera

                    // Update connection state using reactive state
                    updateCameraConnected(true, device)

                    // Cache permission synchronously (fast memory operation, no async overhead needed)
                    cachePermission(device, true)
                    
                    // Launch camera opening asynchronously if needed
                    if (isSurfaceReady) {
                        Logger.i(TAG, "Surface ready, starting camera immediately")
                        fragmentScope.launch {
                            openCamera(mCameraView)
                        }
                    } else {
                        Logger.i(TAG, "Camera connected, waiting for surface to be ready")
                    }

                    Logger.i(TAG, "camera connection. pid: ${device.productId}, vid: ${device.vendorId}")
                }
            }

            override fun onDisConnectDec(device: UsbDevice?, ctrlBlock: USBMonitor.UsbControlBlock?) {
                fragmentScope.launch {
                    closeCamera()
                }
                // Reset connection state using reactive state
                updateCameraConnected(false)
                mRequestPermission.set(false)
            }

            override fun onCancelDev(device: UsbDevice?) {
                mRequestPermission.set(false)
                // Clear camera state when permission is cancelled
                _currentCameraState.value = null
            }
        })
        mCameraClient?.register()
    }

    protected fun unRegisterMultiCamera() {
        mCameraMap.values.forEach {
            it.closeCamera()
            // Properly cancel coroutine scope to prevent memory leaks
            it.destroy()
        }
        mCameraMap.clear()
        mCameraClient?.unRegister()
        mCameraClient?.destroy()
        mCameraClient = null
    }

    protected fun getDeviceList() = mCameraClient?.getDeviceList()

    private fun handleTextureView(textureView: TextureView) {
        textureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(
                surface: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                onSurfaceReady()
            }

            override fun onSurfaceTextureSizeChanged(
                surface: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                fragmentScope.launch {
                    surfaceSizeChanged(width, height)
                }
            }

            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                onSurfaceDestroyed()
                return false
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
            }
        }
    }

    private fun handleSurfaceView(surfaceView: SurfaceView) {
        surfaceView.holder.addCallback(object : SurfaceHolder.Callback {
            override fun surfaceCreated(holder: SurfaceHolder) {
                onSurfaceReady()
            }

            override fun surfaceChanged(
                holder: SurfaceHolder,
                format: Int,
                width: Int,
                height: Int
            ) {
                fragmentScope.launch {
                    surfaceSizeChanged(width, height)
                }
            }

            override fun surfaceDestroyed(holder: SurfaceHolder) {
                onSurfaceDestroyed()
            }
        })
    }

    /**
     * Get current opened camera (now uses StateFlow for reactive state management)
     *
     * @return current camera, see [MultiCameraClient.ICamera]
     */
    protected fun getCurrentCamera(): MultiCameraClient.ICamera? {
        return _currentCameraState.value
    }
    
    /**
     * Get current camera as suspend function for backward compatibility
     * @deprecated Use getCurrentCamera() instead - no longer needs to be suspend
     */
    @Deprecated("Use getCurrentCamera() instead", ReplaceWith("getCurrentCamera()"))
    protected suspend fun getCurrentCameraSuspend(): MultiCameraClient.ICamera? {
        return getCurrentCamera()
    }

    /**
     * Request permission
     *
     * @param device see [UsbDevice]
     */
    protected fun requestPermission(device: UsbDevice?) {
        mRequestPermission.set(true)
        mCameraClient?.requestPermission(device)
    }

    /**
     * Generate camera
     *
     * @param ctx context [Context]
     * @param device Usb device, see [UsbDevice]
     * @return Inheritor assignment camera api policy
     */
    protected open fun generateCamera(ctx: Context, device: UsbDevice): MultiCameraClient.ICamera {
        return CameraUVC(ctx, device)
    }

    /**
     * Get default camera
     *
     * @return Open camera by default, should be [UsbDevice]
     */
    protected open fun getDefaultCamera(): UsbDevice? = null

    /**
     * Capture image (backward compatibility wrapper)
     *
     * @param callBack capture status, see [ICaptureCallBack]
     * @param savePath custom image path
     */
    protected fun captureImage(callBack: ICaptureCallBack, savePath: String? = null) {
        fragmentScope.launch {
            captureImageSuspend(callBack, savePath)
        }
    }
    
    /**
     * Capture image (suspend version for optimal performance)
     */
    private suspend fun captureImageSuspend(callBack: ICaptureCallBack, savePath: String? = null) {
        getCurrentCamera()?.captureImage(callBack, savePath)
    }


    /**
     * Get default effect
     */
    protected fun getDefaultEffect() {
        fragmentScope.launch {
            getCurrentCamera()?.getDefaultEffect()
        }
    }

    /**
     * Switch camera
     *
     * @param usbDevice camera usb device
     */
    protected fun switchCamera(usbDevice: UsbDevice) {
        fragmentScope.launch {
            getCurrentCamera()?.closeCamera()
            try {
                delay(500) // Use coroutine delay instead of Thread.sleep
            } catch (e: Exception) {
                e.printStackTrace()
            }
            requestPermission(usbDevice)
        }
    }

    /**
     * Is camera opened (backward compatibility wrapper)
     *
     * @return camera open status
     */
    protected suspend fun isCameraOpened(): Boolean {
        return getCurrentCamera()?.isCameraOpened() ?: false
    }

    /**
     * Update resolution (backward compatibility wrapper)
     *
     * @param width camera preview width
     * @param height camera preview height
     */
    protected fun updateResolution(width: Int, height: Int) {
        fragmentScope.launch {
            updateResolutionSuspend(width, height)
        }
    }
    
    /**
     * Update resolution (suspend version for optimal performance)
     */
    private suspend fun updateResolutionSuspend(width: Int, height: Int) {
        getCurrentCamera()?.updateResolution(width, height)
    }

    /**
     * Get all preview sizes
     *
     * @param aspectRatio preview size aspect ratio,
     *                      null means getting all preview sizes
     */
    protected fun getAllPreviewSizes(aspectRatio: Double? = null) {
        fragmentScope.launch {
            getCurrentCamera()?.getAllPreviewSizes(aspectRatio)
        }
    }

    /**
     * Add render effect
     *
     * @param effect a effect will be added, only enable opengl render worked, see [AbstractEffect]
     */
    protected fun addRenderEffect(effect: AbstractEffect) {
        fragmentScope.launch {
            getCurrentCamera()?.addRenderEffect(effect)
        }
    }

    /**
     * Remove render effect
     *
     * @param effect a effect will be removed, only enable opengl render worked, see [AbstractEffect]
     */
    protected fun removeRenderEffect(effect: AbstractEffect) {
        fragmentScope.launch {
            getCurrentCamera()?.removeRenderEffect(effect)
        }
    }

    /**
     * Update render effect
     *
     * @param classifyId effect classify id
     * @param effect new effect, null means set none
     */
    protected fun updateRenderEffect(classifyId: Int, effect: AbstractEffect?) {
        fragmentScope.launch {
            getCurrentCamera()?.updateRenderEffect(classifyId, effect)
        }
    }

    /**
     * Start capture H264 & AAC only
     */
    protected fun captureStreamStart() {
        fragmentScope.launch {
            getCurrentCamera()?.captureStreamStart()
        }
    }

    /**
     * Stop capture H264 & AAC only
     */
    protected fun captureStreamStop() {
        fragmentScope.launch {
            getCurrentCamera()?.captureStreamStop()
        }
    }

    /**
     * Add encode data call back
     *
     * @param callBack encode data call back, see [IEncodeDataCallBack]
     */
    protected fun setEncodeDataCallBack(callBack: IEncodeDataCallBack) {
        fragmentScope.launch {
            getCurrentCamera()?.setEncodeDataCallBack(callBack)
        }
    }

    /**
     * Add preview data call back
     *
     * @param callBack preview data call back, see [IPreviewDataCallBack]
     */
    protected fun addPreviewDataCallBack(callBack: IPreviewDataCallBack) {
        fragmentScope.launch {
            getCurrentCamera()?.addPreviewDataCallBack(callBack)
        }
    }

    /**
     * Remove preview data call back
     *
     * @param callBack preview data call back, see [IPreviewDataCallBack]
     */
    fun removePreviewDataCallBack(callBack: IPreviewDataCallBack) {
        fragmentScope.launch {
            getCurrentCamera()?.removePreviewDataCallBack(callBack)
        }
    }

    /**
     * Capture video start
     *
     * @param callBack capture status, see [ICaptureCallBack]
     * @param path custom save path
     * @param durationInSec divided record duration time in seconds
     */
    protected fun captureVideoStart(callBack: ICaptureCallBack, path: String ?= null, durationInSec: Long = 0L) {
        fragmentScope.launch {
            getCurrentCamera()?.captureVideoStart(callBack, path, durationInSec)
        }
    }

    /**
     * Capture video stop
     */
    protected fun captureVideoStop() {
        fragmentScope.launch {
            getCurrentCamera()?.captureVideoStop()
        }
    }

    /**
     * Capture audio start
     *
     * @param callBack capture status, see [ICaptureCallBack]
     * @param path custom save path
     */
    protected fun captureAudioStart(callBack: ICaptureCallBack, path: String ?= null) {
        fragmentScope.launch {
            getCurrentCamera()?.captureAudioStart(callBack, path)
        }
    }

    /**
     * Capture audio stop
     */
    protected fun captureAudioStop() {
        fragmentScope.launch {
            getCurrentCamera()?.captureAudioStop()
        }
    }

    /**
     * Start play mic
     *
     * @param callBack play mic in real-time, see [IPlayCallBack]
     */
    protected fun startPlayMic(callBack: IPlayCallBack? = null) {
        fragmentScope.launch {
            getCurrentCamera()?.startPlayMic(callBack)
        }
    }

    /**
     * Stop play mic
     */
    protected fun stopPlayMic() {
        fragmentScope.launch {
            getCurrentCamera()?.stopPlayMic()
        }
    }

    /**
     * Get current preview size
     *
     * @return camera preview size, see [PreviewSize]
     */
    protected suspend fun getCurrentPreviewSize(): PreviewSize? {
        return getCurrentCamera()?.getCameraRequest()?.let {
            PreviewSize(it.previewWidth, it.previewHeight)
        }
    }

    /**
     * Rotate camera angle
     *
     * @param type rotate angle, null means rotating nothing
     * see [RotateType.ANGLE_90], [RotateType.ANGLE_270],...etc.
     */
    protected fun setRotateType(type: RotateType) {
        fragmentScope.launch {
            getCurrentCamera()?.setRotateType(type)
        }
    }

    /**
     * Helper method to safely execute camera operations with coroutines
     */
    private fun safelyCameraOperation(operation: suspend (MultiCameraClient.ICamera) -> Unit) {
        fragmentScope.launch {
            getCurrentCamera()?.let { camera ->
                operation(camera)
            }
        }
    }

    /**
     * Helper method to safely execute UVC camera operations with coroutines
     */
    private fun safelyUvcCameraOperation(operation: suspend (CameraUVC) -> Unit) {
        fragmentScope.launch {
            getCurrentCamera()?.let { camera ->
                if (camera is CameraUVC) {
                    operation(camera)
                }
            }
        }
    }

    /***********************************************************************************************/
    /*********************************Camera parameter control *************************************/
    /**
     * Send camera command of uvc camera
     *
     * @param command hex value
     * @return control result
     */
    protected fun sendCameraCommand(command: Int) {
        fragmentScope.launch {
            getCurrentCamera()?.let { camera ->
                if (camera !is CameraUVC) {
                    return@let
                }
                camera.sendCameraCommand(command)
            }
        }
    }

    /**
     * Set auto focus
     *
     * @param focus
     */
    protected fun setAutoFocus(focus: Boolean) {
        fragmentScope.launch {
            getCurrentCamera()?.let { camera ->
                if (camera !is CameraUVC) {
                    return@let
                }
                camera.setAutoFocus(focus)
            }
        }
    }

    /**
     * Get auto focus
     *
     * @return is camera auto focus opened
     */
    protected suspend fun getAutoFocus(): Boolean? {
        return getCurrentCamera()?.let { camera ->
            if (camera !is CameraUVC) {
                false
            } else {
                camera.getAutoFocus()
            }
        }
    }

    /**
     * Reset auto focus
     */
    protected fun resetAutoFocus() {
        fragmentScope.launch {
            getCurrentCamera()?.let { camera ->
                if (camera !is CameraUVC) {
                    return@let
                }
                camera.resetAutoFocus()
            }
        }
    }



    /**
     * Set brightness
     *
     * @param brightness camera brightness
     */
    protected fun setBrightness(brightness: Int) {
        safelyUvcCameraOperation { camera ->
            camera.setBrightness(brightness)
        }
    }

    /**
     * Get brightness
     *
     * @return current brightness value
     */
    protected suspend fun getBrightness(): Int? {
        return getCurrentCamera()?.let { camera ->
            if (camera is CameraUVC) {
                camera.getBrightness()
            } else null
        }
    }

    /**
     * Reset brightness
     */
    protected fun resetBrightness() {
        safelyUvcCameraOperation { camera ->
            camera.resetBrightness()
        }
    }

    /**
     * Set contrast
     *
     * @param contrast camera contrast
     */
    protected fun setContrast(contrast: Int) {
        safelyUvcCameraOperation { camera ->
            camera.setContrast(contrast)
        }
    }

    /**
     * Get contrast
     *
     * @return current contrast value
     */
    protected suspend fun getContrast(): Int? {
        return getCurrentCamera()?.let { camera ->
            if (camera is CameraUVC) {
                camera.getContrast()
            } else null
        }
    }

    /**
     * Reset contrast
     */
    protected fun resetContrast() {
        safelyUvcCameraOperation { camera ->
            camera.resetContrast()
        }
    }

    /**
     * Set gain
     *
     * @param gain camera gain
     */
    protected fun setGain(gain: Int) {
        safelyUvcCameraOperation { camera ->
            camera.setGain(gain)
        }
    }

    /**
     * Get gain
     *
     * @return current gain value
     */
    protected suspend fun getGain(): Int? {
        return getCurrentCamera()?.let { camera ->
            if (camera is CameraUVC) {
                camera.getGain()
            } else null
        }
    }

    /**
     * Reset gain
     */
    protected fun resetGain() {
        safelyUvcCameraOperation { camera ->
            camera.resetGain()
        }
    }

    /**
     * Set gamma
     *
     * @param gamma camera gamma
     */
    protected fun setGamma(gamma: Int) {
        safelyUvcCameraOperation { camera ->
            camera.setGamma(gamma)
        }
    }

    /**
     * Get gamma
     *
     * @return current gamma value
     */
    protected suspend fun getGamma(): Int? {
        return getCurrentCamera()?.let { camera ->
            if (camera is CameraUVC) {
                camera.getGamma()
            } else null
        }
    }

    /**
     * Reset gamma
     */
    protected fun resetGamma() {
        safelyUvcCameraOperation { camera ->
            camera.resetGamma()
        }
    }

    /**
     * Set hue
     *
     * @param hue camera hue
     */
    protected fun setHue(hue: Int) {
        safelyUvcCameraOperation { camera ->
            camera.setHue(hue)
        }
    }

    /**
     * Get hue
     *
     * @return current hue value
     */
    protected suspend fun getHue(): Int? {
        return getCurrentCamera()?.let { camera ->
            if (camera is CameraUVC) {
                camera.getHue()
            } else null
        }
    }

    /**
     * Reset hue
     */
    protected fun resetHue() {
        safelyUvcCameraOperation { camera ->
            camera.resetHue()
        }
    }

    /**
     * Set zoom
     *
     * @param zoom camera zoom
     */
    protected fun setZoom(zoom: Int) {
        safelyUvcCameraOperation { camera ->
            camera.setZoom(zoom)
        }
    }

    /**
     * Get zoom
     *
     * @return current zoom value
     */
    protected suspend fun getZoom(): Int? {
        return getCurrentCamera()?.let { camera ->
            if (camera is CameraUVC) {
                camera.getZoom()
            } else null
        }
    }

    /**
     * Reset hue
     */
    protected fun resetZoom() {
        safelyUvcCameraOperation { camera ->
            camera.resetZoom()
        }
    }

    /**
     * Set sharpness
     *
     * @param sharpness camera sharpness
     */
    protected fun setSharpness(sharpness: Int) {
        safelyUvcCameraOperation { camera ->
            camera.setSharpness(sharpness)
        }
    }

    /**
     * Get sharpness
     *
     * @return current sharpness value
     */
    protected suspend fun getSharpness(): Int? {
        return getCurrentCamera()?.let { camera ->
            if (camera is CameraUVC) {
                camera.getSharpness()
            } else null
        }
    }

    /**
     * Reset sharpness
     */
    protected fun resetSharpness() {
        safelyUvcCameraOperation { camera ->
            camera.resetSharpness()
        }
    }

    /**
     * Set saturation
     *
     * @param saturation camera saturation
     */
    protected fun setSaturation(saturation: Int) {
        safelyUvcCameraOperation { camera ->
            camera.setSaturation(saturation)
        }
    }

    /**
     * Get saturation
     *
     * @return current saturation value
     */
    protected suspend fun getSaturation(): Int? {
        return getCurrentCamera()?.let { camera ->
            if (camera is CameraUVC) {
                camera.getSaturation()
            } else null
        }
    }

    /**
     * Reset saturation
     */
    protected fun resetSaturation() {
        safelyUvcCameraOperation { camera ->
            camera.resetSaturation()
        }
    }

    protected suspend fun openCamera(st: IAspectRatio? = null) {
        when (st) {
            is TextureView, is SurfaceView -> {
                st
            }
            else -> {
                null
            }
        }.apply {
            // Check if surface is ready before opening camera
            val surfaceReady = when (this) {
                is TextureView -> this.isAvailable
                is SurfaceView -> this.holder.surface.isValid
                else -> true // For other surface types or null, proceed
            }

            if (surfaceReady) {
                Logger.i(TAG, "Surface is ready, opening camera")
                getCurrentCamera()?.openCamera(this, getCameraRequest())
                // Only set callback if fragment is still attached
                if (isAdded && context != null) {
                    getCurrentCamera()?.setCameraStateCallBack(this@CameraFragment)
                }
            } else {
                Logger.w(TAG, "Surface not ready, delaying camera opening")
                // Retry after a short delay - reduced to 50ms for maximum responsiveness
                delay(50)
                if (isAdded && context != null) {
                    openCamera(st)
                }
            }
        }
    }

    protected suspend fun closeCamera() {
        getCurrentCamera()?.closeCamera()
    }

    override fun onDestroyView() {
        // Clear camera state callback synchronously to prevent crashes
        // No need for coroutine since getCurrentCamera() is no longer suspend
        getCurrentCamera()?.setCameraStateCallBack(null)
        super.onDestroyView()
    }

    private suspend fun surfaceSizeChanged(surfaceWidth: Int, surfaceHeight: Int) {
        Logger.i(TAG, "surfaceSizeChanged: ${surfaceWidth}x${surfaceHeight}")
        getCurrentCamera()?.setRenderSize(surfaceWidth, surfaceHeight)
    }

    /**
     * Called when surface is ready (TextureView available or SurfaceView created)
     * Immediately starts preview if camera is already connected and has permission
     * Uses parallel processing for optimal performance
     */
    private fun onSurfaceReady() {
        updateSurfaceReady(true)
        Logger.i(TAG, "Surface ready, checking if camera can start immediately")

        // Phase 3 Optimization: Parallel processing for surface preparation and camera opening
        fragmentScope.launch {
            coroutineScope {
                val surfaceReportAsync = async {
                    reportSurfaceSizeIfAvailable()
                }
                
                val cameraCheckAsync = async {
                    // If camera is already connected and has permission, start preview immediately
                    if (isCameraConnected && connectedDevice != null && mCameraClient?.hasPermission(connectedDevice) == true) {
                        Logger.i(TAG, "Camera already connected with permission, starting preview immediately")
                        openCamera(mCameraView)
                    }
                }
                
                // Wait for both operations to complete concurrently
                awaitAll(surfaceReportAsync, cameraCheckAsync)
            }
        }
    }

    /**
     * Proactively report surface size for available TextureViews/SurfaceViews
     * This optimization helps avoid the timeout in surface size measurement
     */
    private suspend fun reportSurfaceSizeIfAvailable() {
        mCameraView?.let { view ->
            when (view) {
                is TextureView -> {
                    if (view.isAvailable) {
                        val width = view.width
                        val height = view.height
                        if (width > 0 && height > 0) {
                            Logger.i(TAG, "Proactively reporting TextureView size: ${width}x${height}")
                            getCurrentCamera()?.setRenderSize(width, height)
                        }
                    }
                }
                is SurfaceView -> {
                    if (view.holder.surface.isValid) {
                        val width = view.width
                        val height = view.height
                        if (width > 0 && height > 0) {
                            Logger.i(TAG, "Proactively reporting SurfaceView size: ${width}x${height}")
                            getCurrentCamera()?.setRenderSize(width, height)
                        }
                    }
                }
            }
        }
    }

    /**
     * Called when surface is destroyed
     */
    private fun onSurfaceDestroyed() {
        updateSurfaceReady(false)
        // Note: We don't unregister USB monitor here anymore since it's initialized early
        // This allows device detection to continue even when surface is not ready
    }

    /**
     * Check if device has cached permission to avoid redundant system calls
     */
    private fun hasCachedPermission(device: UsbDevice?): Boolean {
        device ?: return false
        val currentTime = System.currentTimeMillis()

        // Cache is valid for 5 seconds to balance performance and accuracy
        if (currentTime - mLastPermissionCheckTime > 5000) {
            mPermissionCache.clear()
            mLastPermissionCheckTime = currentTime
        }

        return mPermissionCache[device.deviceId] == true
    }

    /**
     * Cache permission state for device to avoid redundant checks
     */
    private fun cachePermission(device: UsbDevice?, hasPermission: Boolean) {
        device ?: return
        mPermissionCache[device.deviceId] = hasPermission
        mLastPermissionCheckTime = System.currentTimeMillis()
    }

    /**
     * Prepare surface for camera in parallel with permission request
     * This reduces the delay after permission is granted
     */
    private fun prepareSurfaceForCamera() {
        mCameraView?.let { view ->
            when (view) {
                is TextureView -> {
                    // Ensure TextureView is properly initialized
                    if (!view.isAvailable) {
                        Logger.i(TAG, "TextureView not yet available, will prepare when ready")
                    } else {
                        Logger.i(TAG, "TextureView already available for camera")
                        // Proactively report surface size if available
                        fragmentScope.launch {
                            reportSurfaceSizeIfAvailable()
                        }
                    }
                }
                is SurfaceView -> {
                    // Ensure SurfaceView holder is ready
                    if (!view.holder.surface.isValid) {
                        Logger.i(TAG, "SurfaceView not yet valid, will prepare when ready")
                    } else {
                        Logger.i(TAG, "SurfaceView already valid for camera")
                        // Proactively report surface size if available
                        fragmentScope.launch {
                            reportSurfaceSizeIfAvailable()
                        }
                    }
                }
                else -> {
                    Logger.i(TAG, "Surface preparation for offscreen rendering")
                }
            }
        }
    }

    private fun getViewLayoutParams(viewGroup: ViewGroup): ViewGroup.LayoutParams {
        return when(viewGroup) {
            is FrameLayout -> {
                FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    getGravity()
                )
            }
            is LinearLayout -> {
                LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT
                ).apply {
                    gravity = getGravity()
                }
            }
            is RelativeLayout -> {
                RelativeLayout.LayoutParams(
                    RelativeLayout.LayoutParams.MATCH_PARENT,
                    RelativeLayout.LayoutParams.MATCH_PARENT
                ).apply{
                    when(getGravity()) {
                        Gravity.TOP -> {
                            addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE)
                        }
                        Gravity.BOTTOM -> {
                            addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE)
                        }
                        else -> {
                            addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE)
                            addRule(RelativeLayout.CENTER_VERTICAL, RelativeLayout.TRUE)
                        }
                    }
                }
            }
            else -> throw IllegalArgumentException("Unsupported container view, " +
                    "you can use FrameLayout or LinearLayout or RelativeLayout")
        }
    }

    /**
     * Get camera view
     *
     * @return CameraView, such as AspectRatioTextureView etc.
     */
    protected abstract fun getCameraView(): IAspectRatio?

    /**
     * Get camera view container
     *
     * @return camera view container, such as FrameLayout ect
     */
    protected abstract fun getCameraViewContainer(): ViewGroup?

    /**
     * Camera render view show gravity
     */
    protected open fun getGravity() = Gravity.CENTER

    protected open fun getCameraRequest(): CameraRequest {
        return CameraRequest.Builder()
            .setPreviewWidth(640)
            .setPreviewHeight(480)
            .setRenderMode(CameraRequest.RenderMode.OPENGL)
            .setDefaultRotateType(RotateType.ANGLE_0)
            .setAudioSource(CameraRequest.AudioSource.SOURCE_SYS_MIC)
            .setPreviewFormat(CameraRequest.PreviewFormat.FORMAT_MJPEG)
            .setAspectRatioShow(true)
            .setCaptureRawImage(false)
            .setRawPreviewData(false)
            .create()
    }

    companion object {
        private const val TAG = "CameraFragment"
    }
}